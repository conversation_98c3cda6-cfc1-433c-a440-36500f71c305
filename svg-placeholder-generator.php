<?php
/**
 * SVG Placeholder Generator
 * สร้าง SVG placeholder สำหรับ lazy loading
 */

function generate_svg_placeholder($width, $height, $text = '', $bg_color = '#f8f9fa', $text_color = '#6c757d') {
    $svg = sprintf(
        '<svg width="%d" height="%d" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 %d %d">
            <rect width="100%%" height="100%%" fill="%s"/>
            <text x="50%%" y="50%%" font-family="Arial, sans-serif" font-size="%d" fill="%s" text-anchor="middle" dy=".3em">%s</text>
        </svg>',
        $width,
        $height,
        $width,
        $height,
        $bg_color,
        min(16, $width / 10), // ขนาดฟอนต์ปรับตามความกว้าง
        $text_color,
        htmlspecialchars($text)
    );
    
    // แปลง SVG เป็น data URL
    return 'data:image/svg+xml;base64,' . base64_encode($svg);
}

function generate_loading_svg_placeholder($width, $height, $text = 'Loading...') {
    $svg = sprintf(
        '<svg width="%d" height="%d" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 %d %d">
            <defs>
                <linearGradient id="shimmer" x1="0%%" y1="0%%" x2="100%%" y2="0%%">
                    <stop offset="0%%" style="stop-color:#f6f7f8;stop-opacity:1" />
                    <stop offset="50%%" style="stop-color:#edeef1;stop-opacity:1" />
                    <stop offset="100%%" style="stop-color:#f6f7f8;stop-opacity:1" />
                </linearGradient>
                <animateTransform attributeName="gradientTransform" attributeType="XML" type="translate" values="-100 0;100 0;-100 0" dur="2s" repeatCount="indefinite"/>
            </defs>
            <rect width="100%%" height="100%%" fill="url(#shimmer)"/>
            <text x="50%%" y="50%%" font-family="Arial, sans-serif" font-size="%d" fill="#999" text-anchor="middle" dy=".3em" opacity="0.7">%s</text>
        </svg>',
        $width,
        $height,
        $width,
        $height,
        min(14, $width / 12),
        htmlspecialchars($text)
    );
    
    return 'data:image/svg+xml;base64,' . base64_encode($svg);
}

// ตัวอย่างการใช้งาน
if (isset($_GET['demo'])) {
    header('Content-Type: text/html; charset=utf-8');
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>SVG Placeholder Demo</title>
        <style>
            body { font-family: Arial, sans-serif; padding: 20px; }
            .demo { margin: 20px 0; }
            img { border: 1px solid #ddd; margin: 10px; }
        </style>
    </head>
    <body>
        <h1>SVG Placeholder Demo</h1>
        
        <div class="demo">
            <h3>Basic Placeholder</h3>
            <img src="<?php echo generate_svg_placeholder(300, 200, 'Banner 300x200'); ?>" alt="Banner">
            <img src="<?php echo generate_svg_placeholder(250, 250, 'Square 250x250'); ?>" alt="Square">
        </div>
        
        <div class="demo">
            <h3>Loading Placeholder</h3>
            <img src="<?php echo generate_loading_svg_placeholder(300, 150, 'Loading...'); ?>" alt="Loading">
            <img src="<?php echo generate_loading_svg_placeholder(200, 300, 'กำลังโหลด'); ?>" alt="Loading Thai">
        </div>
        
        <div class="demo">
            <h3>Custom Colors</h3>
            <img src="<?php echo generate_svg_placeholder(280, 180, 'Custom Blue', '#e3f2fd', '#1976d2'); ?>" alt="Blue">
            <img src="<?php echo generate_svg_placeholder(280, 180, 'Custom Green', '#e8f5e8', '#2e7d32'); ?>" alt="Green">
        </div>
    </body>
    </html>
    <?php
    exit;
}

// API endpoint สำหรับสร้าง SVG
if (isset($_GET['width']) && isset($_GET['height'])) {
    $width = intval($_GET['width']);
    $height = intval($_GET['height']);
    $text = $_GET['text'] ?? '';
    $bg_color = $_GET['bg'] ?? '#f8f9fa';
    $text_color = $_GET['color'] ?? '#6c757d';
    $loading = isset($_GET['loading']);
    
    if ($loading) {
        $svg_data = generate_loading_svg_placeholder($width, $height, $text);
    } else {
        $svg_data = generate_svg_placeholder($width, $height, $text, $bg_color, $text_color);
    }
    
    header('Content-Type: image/svg+xml');
    echo base64_decode(substr($svg_data, strlen('data:image/svg+xml;base64,')));
    exit;
}
?>
