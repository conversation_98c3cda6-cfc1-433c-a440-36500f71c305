<?php

namespace AdManagementPro\Core;

if (!defined('WPINC')) {
    die;
}

class Shortcodes {
    private static $instance;

    public static function instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        $this->register_shortcodes();
    }

    public function register_shortcodes() {
        add_shortcode('ad_position', [$this, 'render_ad_position_shortcode']);
    }

    private function generate_position_svg($position_name, $width, $height, $is_placeholder = false) {
        $pattern_data = '<defs>
            <linearGradient id="darkGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#0f0f23;stop-opacity:0.95" />
                <stop offset="50%" style="stop-color:#1a1a2e;stop-opacity:0.8" />
                <stop offset="100%" style="stop-color:#16213e;stop-opacity:0.9" />
            </linearGradient>
            <pattern id="lightning-checkerboard" width="80" height="80" patternUnits="userSpaceOnUse">
                <rect x="0" y="0" width="40" height="40" fill="transparent"/>
                <rect x="40" y="40" width="40" height="40" fill="transparent"/>
                <text x="20" y="28" font-family="Apple Color Emoji, Segoe UI Emoji, sans-serif" font-size="24" opacity="0.15" text-anchor="middle">⚡</text>
                <text x="60" y="68" font-family="Apple Color Emoji, Segoe UI Emoji, sans-serif" font-size="24" opacity="0.15" text-anchor="middle">⚡</text>
                <rect x="40" y="0" width="40" height="40" fill="rgba(255,255,255,0.02)"/>
                <rect x="0" y="40" width="40" height="40" fill="rgba(255,255,255,0.02)"/>
                <text x="60" y="28" font-family="Apple Color Emoji, Segoe UI Emoji, sans-serif" font-size="20" opacity="0.12" text-anchor="middle">⚡</text>
                <text x="20" y="68" font-family="Apple Color Emoji, Segoe UI Emoji, sans-serif" font-size="20" opacity="0.12" text-anchor="middle">⚡</text>
            </pattern>
        </defs>';

        $pattern_name = 'lightning-checkerboard';
        $gradient_name = 'darkGrad';

        $bg_color = '#050510';
        $border_color = '#6366f1';
        $text_color = '#e2e8f0';

        $is_vertical = $height > $width;

        if ($is_vertical) {
            $position_font_size = min(max($width / 6, 22), 72);
            $dimension_font_size = max($position_font_size * 0.7, 16);
            $text_spacing = $position_font_size * 1.1;
            $vertical_offset = 0;

            $center_y = $height / 2;
            $text_y_position = $center_y - ($text_spacing / 2.2);
            $text_y_dimension = $center_y + ($text_spacing / 2.2);
        } else {
            $position_font_size = min(max($width / 12, 16), 48);
            $dimension_font_size = max($position_font_size * 0.5, 10);
            $text_spacing = $position_font_size * 0.65;
            $vertical_offset = $height * 0.05;

            $center_y = $height / 2;
            $text_y_position = $center_y - ($text_spacing / 2) + $vertical_offset;
            $text_y_dimension = $center_y + ($text_spacing / 2) + $vertical_offset;
        }

        $svg = '<svg width="' . $width . '" height="' . $height . '" xmlns="http://www.w3.org/2000/svg">';
        $svg .= $pattern_data;

        $svg .= '<rect width="100%" height="100%" fill="' . $bg_color . '"/>';
        $svg .= '<rect width="100%" height="100%" fill="url(#' . $gradient_name . ')"/>';
        $svg .= '<rect width="100%" height="100%" fill="url(#' . $pattern_name . ')"/>';

        $svg .= '<rect x="2" y="2" width="' . ($width-4) . '" height="' . ($height-4) . '" fill="none" stroke="' . $border_color . '" stroke-width="2" rx="6" ry="6"/>';

        $letter_spacing = $is_vertical ? '1px' : '2px';
        $position_font_weight = $is_vertical ? '700' : '800';
        $dimension_opacity = $is_vertical ? '0.75' : '0.6';

        $svg .= '<text x="' . ($width/2) . '" y="' . $text_y_position . '" text-anchor="middle" dominant-baseline="central" font-family="JetBrains Mono, Consolas, Monaco, monospace" font-size="' . $position_font_size . '" fill="' . $text_color . '" font-weight="' . $position_font_weight . '" opacity="0.98" letter-spacing="' . $letter_spacing . '">' . htmlspecialchars($position_name) . '</text>';

        $svg .= '<text x="' . ($width/2) . '" y="' . $text_y_dimension . '" text-anchor="middle" dominant-baseline="central" font-family="SF Pro Display, Inter, system-ui, sans-serif" font-size="' . $dimension_font_size . '" fill="' . $text_color . '" opacity="' . $dimension_opacity . '" font-weight="500">' . $width . ' × ' . $height . ' px</text>';

        $svg .= '</svg>';

        return 'data:image/svg+xml;base64,' . base64_encode($svg);
    }

    public function render_ad_position_shortcode($atts) {
        $atts = shortcode_atts(
            [
                'name' => '',
                'class' => '', 
            ],
            $atts,
            'ad_position'
        );

        $position_name = sanitize_text_field($atts['name']);
        if (empty($position_name)) {
            return '<!-- Ad Position: Name not specified -->';
        }

        if (!class_exists('\AdManagementPro\Modules\Shared\PositionManager')) {
            require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
        }

        try {
            $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('public');
            $position = $position_manager->get_position($position_name);

            if (!$position || $position->status !== 'active') {
                return '<!-- Ad Position: ' . esc_html($position_name) . ' not found or inactive -->';
            }

            $ownership_state = $position_manager->get_position_ownership_state($position_name);
            $is_owned = $ownership_state['is_owned'];
            $is_expired = $ownership_state['is_expired'];
            $owner_id = $ownership_state['owner_id'];
            
            if (!$is_owned || $is_expired) {
                $placeholder_image = $this->generate_position_svg($position_name, $position->width, $position->height, true);
                $signup_url = home_url('/sale-page/');

                $output = sprintf(
                    '<a href="%s" class="amp-ad-link amp-placeholder %s" data-position="%s" data-owner-id="%d">',
                    esc_url($signup_url),
                    esc_attr($atts['class']),
                    esc_attr($position_name),
                    esc_attr($owner_id ?: 0)
                );
                $output .= sprintf(
                    '<img class="nl-lazy nl-lazy-bg" src="%s" alt="ตำแหน่งว่าง - %s" style="width: %spx; height: %spx; display: block;">',
                    esc_attr($placeholder_image),
                    esc_attr($position_name),
                    esc_attr($position->width),
                    esc_attr($position->height)
                );
                $output .= '</a>';
                
                return $output;
            }

            $ad_image_url = '';
            $ad_target_url = '';
            $placeholder_svg = $this->generate_position_svg($position_name, $position->width, $position->height, false);

            if (!empty($position->image_url)) {
                $ad_image_url = $position->image_url;
            } else {
                $ad_image_url = $placeholder_svg;

                if (empty($ad_image_url)) {
                    $ad_image_url = 'https://via.placeholder.com/' . $position->width . 'x' . $position->height . '/28a745/ffffff?text=' . urlencode($position_name . ' (' . $position->width . 'x' . $position->height . ')');
                }
            }

            $ad_target_url = !empty($position->target_url) ? $position->target_url : home_url('/sale-page/');

            $tracking_url = home_url('/dynamic-link.php');
            $params = [
                'position' => $position_name,
                'url' => urlencode($ad_target_url)
            ];
            $final_url = add_query_arg($params, $tracking_url);

            $output = sprintf(
                '<a href="%s" target="_blank" rel="nofollow sponsored" class="amp-ad-link %s" data-position="%s" data-owner-id="%d">',
                esc_url($final_url),
                esc_attr($atts['class']),
                esc_attr($position_name),
                esc_attr($owner_id)
            );
            $alt_text = !empty($position->alt_text) ? $position->alt_text : $position_name;

            if (!empty($position->image_url)) {
                $output .= sprintf(
                    '<img class="nl-lazy nl-lazy-bg" src="%s" data-src="%s" alt="%s" style="width: %spx; height: %spx; display: block;">',
                    esc_attr($placeholder_svg),
                    esc_attr($ad_image_url),
                    esc_attr($alt_text),
                    esc_attr($position->width),
                    esc_attr($position->height)
                );
            } else {
                $output .= sprintf(
                    '<img class="nl-lazy nl-lazy-bg" src="%s" alt="%s" style="width: %spx; height: %spx; display: block;">',
                    esc_attr($ad_image_url),
                    esc_attr($alt_text),
                    esc_attr($position->width),
                    esc_attr($position->height)
                );
            }
            $output .= '</a>';
            
            return $output;

        } catch (\Exception $e) {
            error_log('Ad Shortcode Error: ' . $e->getMessage());
            return '<!-- Ad Position: Error -->';
        }
    }

    public static function get_dynamic_link_file_content() {
        return <<<'EOD'
<?php
define('WP_USE_THEMES', false);
if (file_exists(__DIR__ . '/wp-load.php')) {
    require_once(__DIR__ . '/wp-load.php');
} elseif (file_exists(dirname(__DIR__) . '/wp-load.php')) {
    require_once(dirname(__DIR__) . '/wp-load.php');
} else {
    exit('Could not find wp-load.php');
}

ob_start();

$click_stats_file = WP_PLUGIN_DIR . '/ad-management-pro/includes/utils/click-statistics.php';
if (file_exists($click_stats_file)) {
    require_once($click_stats_file);
}

$target_url = isset($_GET['url']) ? urldecode($_GET['url']) : home_url('/');
$position_name = isset($_GET['position']) ? sanitize_text_field($_GET['position']) : 'unknown';

if ($position_name !== 'unknown' && filter_var($target_url, FILTER_VALIDATE_URL)) {
    
    if (!class_exists('\AdManagementPro\Modules\Shared\PositionManager')) {
        $pm_file = WP_PLUGIN_DIR . '/ad-management-pro/includes/modules/shared/class-position-manager.php';
        if(file_exists($pm_file)) {
            require_once($pm_file);
        }
    }

    try {
        $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('public');
        $owner_id = $position_manager->get_position_owner_from_usermeta($position_name);

        if ($owner_id > 0 && function_exists('record_ad_click')) {
            record_ad_click($owner_id, $position_name);
            error_log("Ad Click Recorded: User ID {$owner_id}, Position {$position_name}");
        } else {
            if (function_exists('record_global_click')) {
                record_global_click(1);
                error_log("Global Click Recorded: Position {$position_name} (No Owner)");
            }
            error_log("Ad Click NOT Recorded: Owner ID {$owner_id}, Position {$position_name}");
        }
    } catch (\Exception $e) {
        error_log('Dynamic Link Error: Could not get position owner. ' . $e->getMessage());
    }
}

if (ob_get_length()) {
    ob_end_clean();
}

header("Location: " . $target_url, true, 302);
exit();
EOD;
    }
} 